# Custom Theme-Aware Tailwind Config Optimization Report

Generated on: 2025-07-24T17:18:21.806Z

## 📊 Summary

- **Used classes analyzed**: 910
- **Custom theme colors preserved**: 20
- **Essential utilities preserved**: 8
- **Optimizations performed**: 1
- **Errors encountered**: 1
- **Backup location**: /home/<USER>/gammastack/betshop/Starterkit/tailwind.config.ts.custom-theme-backup

## 🎨 Custom Theme Colors Preserved

- `golden`
- `primary`
- `secondary`
- `success`
- `warning`
- `danger`
- `info`
- `nav`
- `section`
- `filter`
- `elevated`
- `form`
- `background`
- `table`
- `modal`
- `card`
- `bodybg`
- `textmuted`
- `defaultborder`
- `defaulttextcolor`

## 🛠️ Essential Utilities Preserved

- `golden-button`
- `golden-button-shadow`
- `golden-button-hover-shadow`
- `custom-theme-colors`
- `custom-container`
- `custom-container-primary`
- `custom-container-secondary`
- `custom-container-background`

## 🔧 Optimizations Performed

- Removed 23 unused color definitions (preserved custom theme)

## 🎯 Sample Used Classes

- `flex`
- `justify-center`
- `xl:col-span-5`
- `lg:col-span-5`
- `md:col-span-6`
- `sm:col-span-7`
- `rounded-md`
- `overflow-hidden`
- `border-defaultborder`
- `dark:border-defaultborder/10`
- `text-center`
- `grid`
- `items-center`
- `text-textmuted`
- `dark:text-textmuted/50`
- `!border-s`
- `bg-light`
- `h-full`
- `xl:col-span-4`
- `lg:col-span-3`
- `md:col-span-3`
- `sm:col-span-2`
- `lg:col-span-6`
- `sm:col-span-8`
- `!p-[3rem]`
- `font-normal`
- `text-[14px]`
- `xl:col-span-12`
- `text-defaulttextcolor`
- `text-xs`

*... and 880 more*

## ❌ Errors

- **build_test**: Command failed: npm run build:fast
<w> [webpack.cache.PackFileCacheStrategy] Skipped not serializable cache item 'Compilation/modules|/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[11].use[2]!/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[11].use[3]!/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[11].use[4]!/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[11].use[5]!/home/<USER>/gammastack/betshop/Starterkit/app/globals.scss': No serializer registered for PostCSSSyntaxError
<w> while serializing webpack/lib/cache/PackFileCacheStrategy.PackContentItems -> webpack/lib/NormalModule -> webpack/lib/ModuleBuildError -> PostCSSSyntaxError
Failed to compile.

./app/globals.scss:40:6
Syntax error: /home/<USER>/gammastack/betshop/Starterkit/public/assets/scss/custom/_authentication.scss The `dark:bg-bodybg` class does not exist. If `dark:bg-bodybg` is a custom class, make sure it is defined within a `@layer` directive.

./app/globals.scss
Syntax error: /home/<USER>/gammastack/betshop/Starterkit/public/assets/scss/custom/_authentication.scss The `dark:bg-bodybg` class does not exist. If `dark:bg-bodybg` is a custom class, make sure it is defined within a `@layer` directive. (40:6)
    at tryRunOrWebpackError (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:316142)
    at __webpack_require_module__ (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:131548)
    at __nested_webpack_require_161494__ (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:130983)
    at /home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:131840
    at symbolIterator (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/neo-async/async.js:1:14444)
    at done (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/neo-async/async.js:1:14824)
    at Hook.eval [as callAsync] (eval at create (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:14:9224), <anonymous>:15:1)
    at /home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:130703
    at symbolIterator (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/neo-async/async.js:1:14402)
    at timesSync (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/neo-async/async.js:1:5027)
-- inner error --
Syntax error: /home/<USER>/gammastack/betshop/Starterkit/public/assets/scss/custom/_authentication.scss The `dark:bg-bodybg` class does not exist. If `dark:bg-bodybg` is a custom class, make sure it is defined within a `@layer` directive. (40:6)
    at Object.<anonymous> (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[11].use[2]!/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[11].use[3]!/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[11].use[4]!/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[11].use[5]!/home/<USER>/gammastack/betshop/Starterkit/app/globals.scss:1:7)
    at /home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:962742
    at Hook.eval [as call] (eval at create (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:14:9002), <anonymous>:7:1)
    at /home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:131581
    at tryRunOrWebpackError (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:316096)
    at __webpack_require_module__ (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:131548)
    at __nested_webpack_require_161494__ (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:130983)
    at /home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:131840
    at symbolIterator (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/neo-async/async.js:1:14444)
    at done (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/neo-async/async.js:1:14824)

Generated code for /home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[11].use[2]!/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[11].use[3]!/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[11].use[4]!/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[11].use[5]!/home/<USER>/gammastack/betshop/Starterkit/app/globals.scss

Import trace for requested module:
./app/globals.scss


> Build failed because of webpack errors


## 🔄 Rollback Instructions

If issues are detected, restore from backup:
```bash
cp /home/<USER>/gammastack/betshop/Starterkit/tailwind.config.ts.custom-theme-backup /home/<USER>/gammastack/betshop/Starterkit/tailwind.config.ts
```

## 📋 Next Steps

1. Test critical components to ensure custom theme is working
2. Verify golden buttons and custom styling are preserved
3. Check that all custom theme colors are available
4. Validate critical components functionality

---
*Generated by Custom Theme-Aware Tailwind Config Optimizer*
