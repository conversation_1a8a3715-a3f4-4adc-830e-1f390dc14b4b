# Tailwind Config Clean Migration Report

Generated on: 2025-07-24T17:31:17.630Z

## 📊 Migration Summary

- **Migration Status**: ❌ FAILED
- **Actions Performed**: 6
- **Errors Encountered**: 1
- **Backup Location**: /home/<USER>/gammastack/betshop/Starterkit/tailwind.config.ts.bloated-backup

## 📈 Before vs After Comparison

### Before (Bloated Config)
- **Total Lines**: 689
- **Color Definitions**: 168
- **Duplicate Colors**: 51
- **Legacy Definitions**: 22
- **Hardcoded Values**: 227

### After (Clean Config)
- **Total Lines**: N/A
- **Color Definitions**: N/A
- **CSS Variable Usage**: N/A
- **Component Classes**: N/A

### Improvements
Migration failed - no improvements calculated

## 🔧 Actions Performed

- **backup_created**: tailwind.config.ts - 2025-07-24T17:30:54.362Z
- **analyzed_current_config**: N/A - 2025-07-24T17:30:54.364Z
- **replaced_config**: N/A - 2025-07-24T17:30:54.364Z
- **added_clean_variables**: _clean_theme_variables.scss - 2025-07-24T17:30:54.364Z
- **test_passed**: SCSS Compilation - 2025-07-24T17:30:55.545Z
- **test_passed**: TypeScript Check - 2025-07-24T17:30:58.378Z

## ❌ Errors Encountered

- **test_failed**: Command failed: npm run build:fast
<w> [webpack.cache.PackFileCacheStrategy] Skipped not serializable cache item 'Compilation/modules|/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[11].use[2]!/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[11].use[3]!/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[11].use[4]!/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[11].use[5]!/home/<USER>/gammastack/betshop/Starterkit/app/globals.scss': No serializer registered for PostCSSSyntaxError
<w> while serializing webpack/lib/cache/PackFileCacheStrategy.PackContentItems -> webpack/lib/NormalModule -> webpack/lib/ModuleBuildError -> PostCSSSyntaxError
Failed to compile.

./app/globals.scss:7:4
Syntax error: /home/<USER>/gammastack/betshop/Starterkit/public/assets/scss/custom/_custom.scss The `bg-light` class does not exist. If `bg-light` is a custom class, make sure it is defined within a `@layer` directive.

./app/globals.scss
Syntax error: /home/<USER>/gammastack/betshop/Starterkit/public/assets/scss/custom/_custom.scss The `bg-light` class does not exist. If `bg-light` is a custom class, make sure it is defined within a `@layer` directive. (7:4)
    at tryRunOrWebpackError (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:316142)
    at __webpack_require_module__ (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:131548)
    at __nested_webpack_require_161494__ (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:130983)
    at /home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:131840
    at symbolIterator (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/neo-async/async.js:1:14444)
    at done (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/neo-async/async.js:1:14824)
    at Hook.eval [as callAsync] (eval at create (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:14:9224), <anonymous>:15:1)
    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:14:6378)
    at /home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:130703
    at symbolIterator (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/neo-async/async.js:1:14402)
-- inner error --
Syntax error: /home/<USER>/gammastack/betshop/Starterkit/public/assets/scss/custom/_custom.scss The `bg-light` class does not exist. If `bg-light` is a custom class, make sure it is defined within a `@layer` directive. (7:4)
    at Object.<anonymous> (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[11].use[2]!/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[11].use[3]!/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[11].use[4]!/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[11].use[5]!/home/<USER>/gammastack/betshop/Starterkit/app/globals.scss:1:7)
    at /home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:962742
    at Hook.eval [as call] (eval at create (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:14:9002), <anonymous>:7:1)
    at Hook.CALL_DELEGATE [as _call] (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:14:6272)
    at /home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:131581
    at tryRunOrWebpackError (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:316096)
    at __webpack_require_module__ (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:131548)
    at __nested_webpack_require_161494__ (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:130983)
    at /home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/webpack/bundle5.js:29:131840
    at symbolIterator (/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/neo-async/async.js:1:14444)

Generated code for /home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[11].use[2]!/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[11].use[3]!/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[11].use[4]!/home/<USER>/gammastack/betshop/Starterkit/node_modules/.pnpm/next@15.2.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.89.2/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[11].use[5]!/home/<USER>/gammastack/betshop/Starterkit/app/globals.scss

Import trace for requested module:
./app/globals.scss


> Build failed because of webpack errors


## 🎨 New Features Available

Migration failed - new features not available

## 🔄 Rollback Instructions

If issues are detected, restore the original config:
```bash
cp /home/<USER>/gammastack/betshop/Starterkit/tailwind.config.ts.bloated-backup /home/<USER>/gammastack/betshop/Starterkit/tailwind.config.ts
npm run sass
npm run build
```

## 📋 Next Steps


1. ❌ Migration failed - check errors above
2. Fix any issues and retry migration
3. Consider manual cleanup if automated migration continues to fail


---
*Generated by Clean Config Migrator*
