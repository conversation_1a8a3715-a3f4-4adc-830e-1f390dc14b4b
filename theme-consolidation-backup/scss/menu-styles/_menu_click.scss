/* Start:: menu_click */
[data-nav-style="menu-click"][data-nav-layout="horizontal"] {
    @extend .menu-click;
    @media (min-width: 992px) {
        .app-sidebar {
            .side-menu__item {
                @apply flex ps-3 pe-7 py-[0.93rem];
            }
            .side-menu__item {
                @apply flex #{!important};
            }
            .side-menu__icon {
                @apply me-2 mb-0;
            }
            .side-menu__angle {
                @apply block #{!important};
            }
            .slide.has-sub .slide-menu {
                @apply start-auto #{!important};
                &.active {
                    @apply inset-x-auto #{!important};
                }
                &.child1 {
                    @apply top-full #{!important};
                }
            }
        }
        .app-header {
            @apply ps-[9.5rem];
        }
    }
}
[data-nav-style="menu-click"][data-toggled="menu-click-closed"] {
    @extend .menu-click;
    .app-header {
        @apply ps-[9.5rem];
    }
}
[data-nav-layout="vertical"][data-nav-style="menu-click"] {
    @media (min-width: 992px) {
        [data-menu-styles="color"] {
            .app-sidebar .main-menu .slide .side-menu__item:hover > .side-menu__item .side-menu__icon {
                @apply text-white fill-white;
            }
        }
    }
}
[data-nav-style="menu-click"][data-nav-layout="vertical"][data-toggled="menu-click-closed"] {
    @media (min-width: 992px) {
        .app-sidebar {
            @apply absolute;
            .side-menu__item {
                @apply mx-0 my-1 rounded-lg;
                &:last-child {
                    @apply mx-0 my-1;
                }
            }
            .side-menu__icon{
                @apply inline-flex mb-2 me-0 #{!important};
            }
        }
        .app-sidebar {
            .slide .slide-menu {
                &.child1,&.child2,&.child3 {
                    @apply rounded-[0_0.5rem_0.5rem_0];
                }
            } 
        }
        &[dir="rtl"] {
            .app-sidebar {
                .slide .slide-menu {
                    &.child1,&.child2,&.child3 {
                        @apply rounded-[0.5rem_0_0_0.5rem];
                    }
                } 
            }
        }
    }
}
.menu-click {
    @media (min-width: 992px) {
        .app-sidebar {
            @apply w-[9.5rem];
            .main-sidebar {
                @apply overflow-visible h-[90%];
            }
            .main-sidebar-header {
                @apply w-[9.5rem] justify-center;
            }
            // .side-menu__icon {
            //     @apply me-0 mb-2;
            // }
            .slide {
                @apply p-0;
            }
            .slide-menu {
                &.child1,
                &.child2,
                &.child3 {
                    @apply min-w-[12rem];
                    .slide {
                        .side-menu__item {
                            @apply text-start;
                            &:before {
                                @apply hidden;
                            }
                        }
                    }
                    .side-menu__angle {
                        @apply block end-2 top-[0.65rem];
                    }
                    .slide.has-sub,.slide {
                        &.side-menu__label1 {
                            @apply hidden;
                        }
                    }
                }
            }
            .slide__category,
            .side-menu__angle {
                @apply hidden;
            }
            .side-menu__item,
            .side-menu__label {
                @apply block text-center;
            }
            .slide.has-sub .slide-menu {
                @apply absolute bg-white dark:bg-bodybg shadow-[0_0_0.375rem_rgba(0,0,0,0.1)] transition-none start-[9.5rem] top-auto #{!important};
                &.child2,
                &.child3 {
                    @apply start-48 #{!important};
                }
            }
            .simplebar-content-wrapper {
                position: initial;
            }
            .simplebar-mask {
                position: inherit;
            }
            .simplebar-placeholder {
                @apply h-auto #{!important};
            }
        }
        .app-content {
            @apply ms-[9.5rem];
        }
    }
    @media (max-width: 991.98px) {
        .app-sidebar {
            @apply w-60;
        }
    }
}
@media (min-width: 992px) {
    [data-nav-layout="vertical"] {
        &[data-nav-style="menu-click"][data-toggled="menu-click-closed"] { 
            .app-sidebar {
                .slide .side-menu__label {
                    .badge {
                        @apply hidden;
                    }
                }
            }
            &[data-bg-img="bgimg1"],&[data-bg-img="bgimg2"],&[data-bg-img="bgimg3"],&[data-bg-img="bgimg4"],&[data-bg-img="bgimg5"] {
                .app-sidebar {
                    .main-sidebar-header {
                        @apply backdrop-blur-[30px];
                    }
                }
            }
        }
    }
}
/* End:: menu_click */