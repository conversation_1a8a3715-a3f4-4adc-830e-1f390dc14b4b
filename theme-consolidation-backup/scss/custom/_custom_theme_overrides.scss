
/* Custom Theme Classes - Replacing Xtentra Vendor Classes */

.custom-theme-colors {
  /* Custom color palette for theme switching */
  --primary-color: var(--primary);
  --secondary-color: var(--secondary);
  --success-color: var(--success);
  --warning-color: var(--warning);
  --danger-color: var(--danger);
  --info-color: var(--info);
  --golden-color: var(--golden);
}

.custom-container {
  /* Custom container styling */
  background: rgb(var(--background));
  border: 1px solid rgb(var(--defaultborder));
  border-radius: 0.5rem;
  padding: 1rem;
}

.custom-container-primary {
  /* Primary themed container */
  background: rgb(var(--primary) / 0.1);
  border: 1px solid rgb(var(--primary) / 0.2);
  color: rgb(var(--primary));
}

.custom-container-secondary {
  /* Secondary themed container */
  background: rgb(var(--secondary) / 0.1);
  border: 1px solid rgb(var(--secondary) / 0.2);
  color: rgb(var(--secondary));
}

.custom-container-background {
  /* Background themed container */
  background: rgb(var(--bodybg));
  border: 1px solid rgb(var(--defaultborder));
  color: rgb(var(--defaulttextcolor));
}

/* Golden button theme consistency */
.btn-golden,
.custom-golden-btn {
  background: var(--golden-button);
  border: 1px solid var(--golden-button);
  color: white;
  box-shadow: var(--golden-button-shadow);
  
  &:hover {
    box-shadow: var(--golden-button-hover-shadow);
    transform: translateY(-1px);
  }
}

/* Ensure custom theme takes precedence over vendor styles */
.custom-theme-override {
  /* Force custom theme colors */
  --bs-primary: var(--primary) !important;
  --bs-secondary: var(--secondary) !important;
  --bs-success: var(--success) !important;
  --bs-warning: var(--warning) !important;
  --bs-danger: var(--danger) !important;
  --bs-info: var(--info) !important;
}
