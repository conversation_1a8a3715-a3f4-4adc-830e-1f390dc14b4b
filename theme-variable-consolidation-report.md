# Theme Variable Consolidation Report

Generated on: 2025-07-24T17:15:27.410Z

## 📊 Summary

- **Actions performed**: 21
- **Inconsistencies found**: 118
- **Errors encountered**: 0
- **Backup location**: /home/<USER>/gammastack/betshop/Starterkit/theme-consolidation-backup

## 🎨 Custom Theme Variables

- `--primary`: var(--primary)
- `--secondary`: var(--secondary)
- `--success`: var(--success)
- `--warning`: var(--warning)
- `--danger`: var(--danger)
- `--info`: var(--info)
- `--golden`: var(--golden)
- `--golden-button`: var(--golden-button)
- `--bodybg`: var(--bodybg)
- `--background`: var(--background)
- `--nav`: var(--nav)
- `--section`: var(--section)
- `--filter`: var(--filter)
- `--elevated`: var(--elevated)
- `--form`: var(--form)
- `--table`: var(--table)
- `--modal`: var(--modal)
- `--card`: var(--card)
- `--defaulttextcolor`: var(--defaulttextcolor)
- `--textmuted`: var(--textmuted)
- `--defaultborder`: var(--defaultborder)

## 🔧 Actions Performed

- **fixed_theme_inconsistencies**: app/(components)/(content-layout)/sportsbook/components/SportsbookPageClient.tsx (2 inconsistencies fixed) - 2025-07-24T17:14:38.505Z
- **fixed_theme_inconsistencies**: app/(components)/(content-layout)/user-management/create/page.tsx (2 inconsistencies fixed) - 2025-07-24T17:14:38.505Z
- **fixed_theme_inconsistencies**: app/(components)/(content-layout)/user-management/deactivate/[id]/DeactivateUserPageClient.tsx (2 inconsistencies fixed) - 2025-07-24T17:14:38.505Z
- **fixed_theme_inconsistencies**: app/(components)/(content-layout)/user-management/edit/[id]/EditUserPageClient.tsx (2 inconsistencies fixed) - 2025-07-24T17:14:38.506Z
- **fixed_theme_inconsistencies**: app/not-found.tsx (2 inconsistencies fixed) - 2025-07-24T17:14:38.506Z
- **fixed_theme_inconsistencies**: shared/UI/buttons/PrimaryButton.tsx (2 inconsistencies fixed) - 2025-07-24T17:14:38.506Z
- **fixed_theme_inconsistencies**: shared/UI/cards/SportsCategoryCard.tsx (2 inconsistencies fixed) - 2025-07-24T17:14:38.506Z
- **fixed_theme_inconsistencies**: shared/UI/components/AccessToggleSwitch.tsx (8 inconsistencies fixed) - 2025-07-24T17:14:38.506Z
- **fixed_theme_inconsistencies**: shared/UI/filters/GlobalFilterSection.tsx (2 inconsistencies fixed) - 2025-07-24T17:14:38.506Z
- **fixed_theme_inconsistencies**: shared/UI/filters/themes/filterThemes.ts (2 inconsistencies fixed) - 2025-07-24T17:14:38.506Z
- **fixed_theme_inconsistencies**: shared/UI/forms/SignInFormUI.tsx (18 inconsistencies fixed) - 2025-07-24T17:14:38.506Z
- **fixed_theme_inconsistencies**: shared/UI/modals/BetSettlementModal.tsx (2 inconsistencies fixed) - 2025-07-24T17:14:38.506Z
- **fixed_theme_inconsistencies**: shared/UI/modals/ExportCenterModal.tsx (8 inconsistencies fixed) - 2025-07-24T17:14:38.506Z
- **fixed_theme_inconsistencies**: shared/UI/modals/GlobalUserManagementModal.tsx (2 inconsistencies fixed) - 2025-07-24T17:14:38.506Z
- **fixed_theme_inconsistencies**: shared/UI/modals/UserManagementModal.tsx (2 inconsistencies fixed) - 2025-07-24T17:14:38.507Z
- **fixed_theme_inconsistencies**: shared/UI/modals/UserManagementModalForm.tsx (2 inconsistencies fixed) - 2025-07-24T17:14:38.507Z
- **fixed_theme_inconsistencies**: shared/UI/modals/WalletTransactionModal.tsx (4 inconsistencies fixed) - 2025-07-24T17:14:38.507Z
- **fixed_theme_inconsistencies**: shared/UI/sportsbook/InlineSportsbookUI.tsx (2 inconsistencies fixed) - 2025-07-24T17:14:38.507Z
- **fixed_theme_inconsistencies**: shared/components/tables/ExportCenterTableColumns.tsx (2 inconsistencies fixed) - 2025-07-24T17:14:38.507Z
- **fixed_theme_inconsistencies**: shared/layouts-components/header/components/HeaderProfileIcon.tsx (2 inconsistencies fixed) - 2025-07-24T17:14:38.507Z
- **fixed_theme_inconsistencies**: shared/layouts-components/navigation/HorizontalNavigation.tsx (14 inconsistencies fixed) - 2025-07-24T17:14:38.507Z

## ⚠️ Inconsistencies Found and Fixed

- **public/assets/scss/custom/_custom.scss:2751** - bootstrap_variable: `@apply ms-[calc(var(--bs-width)*-1)]` → `  @apply ms-[calc(var(--bs-width)*-1)]
`
- **public/assets/scss/util/_typography.scss:19** - bootstrap_variable: `@apply text-primary decoration-[rgba(var(--primary-rgb),var(--bs-link-underline-opacity,1))];` → `  @apply text-primary decoration-[rgba(var(--primary-rgb),var(--bs-link-underline-opacity,1))];
`
- **public/assets/scss/util/_typography.scss:24** - bootstrap_variable: `@apply decoration-[rgba(var(--primary-rgb),var(--bs-link-underline-opacity,1))] text-primary;` → `  @apply decoration-[rgba(var(--primary-rgb),var(--bs-link-underline-opacity,1))] text-primary;
`
- **public/assets/scss/util/_typography.scss:27** - bootstrap_variable: `@apply text-primarytint1color decoration-[rgba(var(--primary-tint1-rgb),var(--bs-link-underline-opacity,1))];` → `  @apply text-primarytint1color decoration-[rgba(var(--primary-tint1-rgb),var(--bs-link-underline-opacity,1))];
`
- **public/assets/scss/util/_typography.scss:32** - bootstrap_variable: `@apply decoration-[rgba(var(--primary-tint1-rgb),var(--bs-link-underline-opacity,1))] text-primary/10;` → `  @apply decoration-[rgba(var(--primary-tint1-rgb),var(--bs-link-underline-opacity,1))] text-primary/10;
`
- **public/assets/scss/util/_typography.scss:36** - bootstrap_variable: `@apply text-primary/20 decoration-[rgba(var(--primary-tint2-rgb),var(--bs-link-underline-opacity,1))];` → `  @apply text-primary/20 decoration-[rgba(var(--primary-tint2-rgb),var(--bs-link-underline-opacity,1))];
`
- **public/assets/scss/util/_typography.scss:41** - bootstrap_variable: `@apply decoration-[rgba(var(--primary-tint1-rgb),var(--bs-link-underline-opacity,1))] text-primary/20;` → `  @apply decoration-[rgba(var(--primary-tint1-rgb),var(--bs-link-underline-opacity,1))] text-primary/20;
`
- **public/assets/scss/util/_typography.scss:45** - bootstrap_variable: `@apply text-primary/30 decoration-[rgba(var(--primary-tint3-rgb),var(--bs-link-underline-opacity,1))];` → `  @apply text-primary/30 decoration-[rgba(var(--primary-tint3-rgb),var(--bs-link-underline-opacity,1))];
`
- **public/assets/scss/util/_typography.scss:50** - bootstrap_variable: `@apply decoration-[rgba(var(--primary-tint1-rgb),var(--bs-link-underline-opacity,1))] text-primary/10;` → `  @apply decoration-[rgba(var(--primary-tint1-rgb),var(--bs-link-underline-opacity,1))] text-primary/10;
`
- **public/assets/scss/util/_typography.scss:54** - bootstrap_variable: `@apply text-secondary decoration-[rgba(var(--secondary-rgb),var(--bs-link-underline-opacity,1))];` → `  @apply text-secondary decoration-[rgba(var(--secondary-rgb),var(--bs-link-underline-opacity,1))];
`
- **public/assets/scss/util/_typography.scss:59** - bootstrap_variable: `@apply decoration-[rgba(var(--secondary-rgb),var(--bs-link-underline-opacity,1))] text-secondary;` → `  @apply decoration-[rgba(var(--secondary-rgb),var(--bs-link-underline-opacity,1))] text-secondary;
`
- **public/assets/scss/util/_typography.scss:62** - bootstrap_variable: `@apply text-success decoration-[rgba(var(--success-rgb),var(--bs-link-underline-opacity,1))];` → `  @apply text-success decoration-[rgba(var(--success-rgb),var(--bs-link-underline-opacity,1))];
`
- **public/assets/scss/util/_typography.scss:67** - bootstrap_variable: `@apply decoration-[rgba(var(--success-rgb),var(--bs-link-underline-opacity,1))] text-success;` → `  @apply decoration-[rgba(var(--success-rgb),var(--bs-link-underline-opacity,1))] text-success;
`
- **public/assets/scss/util/_typography.scss:71** - bootstrap_variable: `@apply text-danger decoration-[rgba(var(--danger-rgb),var(--bs-link-underline-opacity,1))];` → `  @apply text-danger decoration-[rgba(var(--danger-rgb),var(--bs-link-underline-opacity,1))];
`
- **public/assets/scss/util/_typography.scss:76** - bootstrap_variable: `@apply decoration-[rgba(var(--danger-rgb),var(--bs-link-underline-opacity,1))] text-danger;` → `  @apply decoration-[rgba(var(--danger-rgb),var(--bs-link-underline-opacity,1))] text-danger;
`
- **public/assets/scss/util/_typography.scss:79** - bootstrap_variable: `@apply text-warning decoration-[rgba(var(--warning-rgb),var(--bs-link-underline-opacity,1))];` → `  @apply text-warning decoration-[rgba(var(--warning-rgb),var(--bs-link-underline-opacity,1))];
`
- **public/assets/scss/util/_typography.scss:84** - bootstrap_variable: `@apply decoration-[rgba(var(--warning-rgb),var(--bs-link-underline-opacity,1))] text-warning;` → `  @apply decoration-[rgba(var(--warning-rgb),var(--bs-link-underline-opacity,1))] text-warning;
`
- **public/assets/scss/util/_typography.scss:87** - bootstrap_variable: `@apply text-info decoration-[rgba(var(--info-rgb),var(--bs-link-underline-opacity,1))];` → `  @apply text-info decoration-[rgba(var(--info-rgb),var(--bs-link-underline-opacity,1))];
`
- **public/assets/scss/util/_typography.scss:92** - bootstrap_variable: `@apply decoration-[rgba(var(--info-rgb),var(--bs-link-underline-opacity,1))] text-info;` → `  @apply decoration-[rgba(var(--info-rgb),var(--bs-link-underline-opacity,1))] text-info;
`
- **public/assets/scss/util/_typography.scss:95** - bootstrap_variable: `@apply text-light decoration-[rgba(var(--light-rgb),var(--bs-link-underline-opacity,1))];` → `  @apply text-light decoration-[rgba(var(--light-rgb),var(--bs-link-underline-opacity,1))];
`


*... and 98 more*

## ❌ Errors Encountered

No errors encountered ✅

## 🔄 Rollback Instructions

If issues are detected, restore from backup:
```bash
cp -r /home/<USER>/gammastack/betshop/Starterkit/theme-consolidation-backup/scss/* public/assets/scss/
cp /home/<USER>/gammastack/betshop/Starterkit/theme-consolidation-backup/tailwind.config.ts tailwind.config.ts
```

## 📋 Next Steps

1. Test critical components to ensure theme consistency
2. Verify golden buttons and custom styling work correctly
3. Check that all components use custom theme variables
4. Proceed with Tailwind config optimization

---
*Generated by Theme Variable Consolidator*
