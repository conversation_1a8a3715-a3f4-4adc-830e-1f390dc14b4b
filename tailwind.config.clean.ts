import forms from '@tailwindcss/forms';
import type { Config } from 'tailwindcss';
import plugin from 'tailwindcss/plugin';
// @ts-ignore - preline doesn't have TypeScript definitions
import preline from 'preline/plugin';

const config: Config = {
  darkMode: "class",
  content: [
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./shared/**/*.{js,ts,jsx,tsx,mdx}",
    './node_modules/preline/preline.js',
  ],
  theme: {
    screens: {
      sm: "480px",
      md: "768px", 
      lg: "992px",
      xl: "1200px",
      xxl: "1400px",
      xxxl: "1800px",
    },
    fontFamily: {
      defaultfont: ["var(--font-poppins)", "Poppins", "sans-serif"],
      rubik: ["var(--font-rubik)", "Rubik", "sans-serif"],
      mont: ["Montserrat", "sans-serif"],
      remix: ["remixicon"],
      tabler: ["tabler-icons"],
      bootstrap: ["bootstrap-icons"],
    },
    fontSize: {
      defaultsize: '0.8125rem',
      xs: '0.75rem',
      sm: '0.875rem', 
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
      '6xl': '3.75rem',
    },
    extend: {
      colors: {
        // === CORE THEME COLORS (Using CSS Variables) ===
        primary: "rgb(var(--primary) / <alpha-value>)",
        secondary: "rgb(var(--secondary) / <alpha-value>)",
        success: "rgb(var(--success) / <alpha-value>)",
        warning: "rgb(var(--warning) / <alpha-value>)",
        danger: "rgb(var(--danger) / <alpha-value>)",
        info: "rgb(var(--info) / <alpha-value>)",
        
        // === CUSTOM GOLDEN THEME ===
        golden: "rgb(var(--golden) / <alpha-value>)",
        "golden-dark": "rgb(var(--golden-dark) / <alpha-value>)",
        
        // === LAYOUT COLORS ===
        background: "rgb(var(--background) / <alpha-value>)",
        bodybg: "rgb(var(--bodybg) / <alpha-value>)",
        nav: "rgb(var(--nav) / <alpha-value>)",
        section: "rgb(var(--section) / <alpha-value>)",
        elevated: "rgb(var(--elevated) / <alpha-value>)",
        
        // === COMPONENT COLORS ===
        "form-input": "rgb(var(--form-input) / <alpha-value>)",
        "form-bg": "rgb(var(--form-bg) / <alpha-value>)",
        "table-section": "rgb(var(--table-section) / <alpha-value>)",
        "table-head": "rgb(var(--table-head) / <alpha-value>)",
        "modal-header": "rgb(var(--modal-header) / <alpha-value>)",
        
        // === TEXT COLORS ===
        defaulttextcolor: "rgb(var(--defaulttextcolor) / <alpha-value>)",
        textmuted: "rgb(var(--textmuted) / <alpha-value>)",
        
        // === BORDER COLORS ===
        defaultborder: "rgb(var(--defaultborder) / <alpha-value>)",
        
        // === NOTIFICATION COLORS ===
        "success-notification": "rgb(var(--success-notification) / <alpha-value>)",
        "error-notification": "rgb(var(--error-notification) / <alpha-value>)",
        "warning-notification": "rgb(var(--warning-notification) / <alpha-value>)",
        "info-notification": "rgb(var(--info-notification) / <alpha-value>)",
        
        "success-message": "rgb(var(--success-message) / <alpha-value>)",
        "error-message": "rgb(var(--error-message) / <alpha-value>)",
        "warning-message": "rgb(var(--warning-message) / <alpha-value>)",
        "info-message": "rgb(var(--info-message) / <alpha-value>)",
      },
      
      // === CUSTOM UTILITIES ===
      backgroundImage: {
        'golden-gradient': 'linear-gradient(135deg, rgb(var(--golden)), rgb(var(--golden-dark)))',
        'primary-gradient': 'linear-gradient(135deg, rgb(var(--primary)), rgb(var(--primary-dark)))',
      },
      
      boxShadow: {
        'golden': '0 4px 14px 0 rgba(225, 182, 73, 0.39)',
        'golden-hover': '0 6px 20px rgba(225, 182, 73, 0.4)',
        'custom': '0 2px 8px rgba(0, 0, 0, 0.1)',
      },
      
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
      },
      
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  
  plugins: [
    forms,
    preline,
    
    // === CUSTOM COMPONENT CLASSES ===
    plugin(function({ addComponents, theme }) {
      addComponents({
        // === GOLDEN BUTTON COMPONENTS ===
        '.btn-golden': {
          background: 'linear-gradient(135deg, rgb(var(--golden)), rgb(var(--golden-dark)))',
          border: '1px solid rgb(var(--golden))',
          color: 'white',
          padding: '0.5rem 1rem',
          borderRadius: '0.5rem',
          fontWeight: '600',
          boxShadow: theme('boxShadow.golden'),
          transition: 'all 0.3s ease',
          '&:hover': {
            boxShadow: theme('boxShadow.golden-hover'),
            transform: 'translateY(-1px)',
          },
          '&:active': {
            transform: 'translateY(0)',
          },
        },
        
        // === CUSTOM CONTAINER COMPONENTS ===
        '.custom-container': {
          backgroundColor: 'rgb(var(--background))',
          border: '1px solid rgb(var(--defaultborder))',
          borderRadius: '0.5rem',
          padding: '1rem',
        },
        
        '.custom-container-primary': {
          backgroundColor: 'rgb(var(--primary) / 0.1)',
          border: '1px solid rgb(var(--primary) / 0.2)',
          color: 'rgb(var(--primary))',
          borderRadius: '0.5rem',
          padding: '1rem',
        },
        
        '.custom-container-elevated': {
          backgroundColor: 'rgb(var(--elevated))',
          border: '1px solid rgb(var(--defaultborder))',
          borderRadius: '0.5rem',
          padding: '1rem',
          boxShadow: theme('boxShadow.custom'),
        },
        
        // === FORM COMPONENTS ===
        '.form-input-custom': {
          backgroundColor: 'rgb(var(--form-input))',
          border: '1px solid rgb(var(--defaultborder))',
          borderRadius: '0.5rem',
          padding: '0.75rem',
          color: 'rgb(var(--defaulttextcolor))',
          '&::placeholder': {
            color: 'rgb(var(--textmuted))',
          },
          '&:focus': {
            borderColor: 'rgb(var(--golden))',
            outline: 'none',
            boxShadow: '0 0 0 3px rgb(var(--golden) / 0.1)',
          },
        },
        
        // === TABLE COMPONENTS ===
        '.table-custom': {
          backgroundColor: 'rgb(var(--elevated))',
          borderRadius: '0.5rem',
          overflow: 'hidden',
          '& thead': {
            backgroundColor: 'rgb(var(--table-head))',
          },
          '& th': {
            padding: '1rem',
            fontWeight: '600',
            color: 'rgb(var(--defaulttextcolor))',
            borderBottom: '1px solid rgb(var(--defaultborder))',
          },
          '& td': {
            padding: '1rem',
            color: 'rgb(var(--textmuted))',
            borderBottom: '1px solid rgb(var(--defaultborder) / 0.5)',
          },
          '& tr:hover': {
            backgroundColor: 'rgb(var(--background) / 0.5)',
          },
        },
        
        // === NOTIFICATION COMPONENTS ===
        '.notification-success': {
          backgroundColor: 'rgb(var(--success-notification))',
          border: '1px solid rgb(var(--success))',
          color: 'rgb(var(--success-message))',
          borderRadius: '0.5rem',
          padding: '1rem',
        },
        
        '.notification-error': {
          backgroundColor: 'rgb(var(--error-notification))',
          border: '1px solid rgb(var(--danger))',
          color: 'rgb(var(--error-message))',
          borderRadius: '0.5rem',
          padding: '1rem',
        },
        
        '.notification-warning': {
          backgroundColor: 'rgb(var(--warning-notification))',
          border: '1px solid rgb(var(--warning))',
          color: 'rgb(var(--warning-message))',
          borderRadius: '0.5rem',
          padding: '1rem',
        },
        
        '.notification-info': {
          backgroundColor: 'rgb(var(--info-notification))',
          border: '1px solid rgb(var(--info))',
          color: 'rgb(var(--info-message))',
          borderRadius: '0.5rem',
          padding: '1rem',
        },
      });
    }),
    
    // === CUSTOM UTILITIES ===
    plugin(function({ addUtilities }) {
      addUtilities({
        '.text-shadow': {
          textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
        },
        '.text-shadow-lg': {
          textShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
        },
        '.backdrop-blur-custom': {
          backdropFilter: 'blur(10px)',
        },
      });
    }),
  ],
};

export default config;
